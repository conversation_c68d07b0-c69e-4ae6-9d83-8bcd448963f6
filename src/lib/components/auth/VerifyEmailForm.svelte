<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { nhost } from '$lib/stores/nhost.js';
  import toast from 'svelte-5-french-toast';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { CheckCircle, XCircle, AlertCircle, Clock, ArrowLeft } from 'lucide-svelte';

  interface Props {
    turnstileToken: string;
  }

  let { turnstileToken }: Props = $props();

  let isProcessing = $state(true);
  let verificationStatus = $state<'processing' | 'success' | 'failed' | 'already_verified' | 'invalid'>('processing');
  let userEmail = $state('');
  let errorMessage = $state('');

  onMount(async () => {
    await handleVerification();
  });

  async function handleVerification() {
    try {
      // Get verification parameters from URL
      const token = $page.url.searchParams.get('refreshToken');
      const type = $page.url.searchParams.get('type');

      console.log('Verification attempt:', { token: token?.substring(0, 8) + '...', type });

      if (!token || type !== 'verifyEmail') {
        verificationStatus = 'invalid';
        errorMessage = 'Invalid verification link';
        isProcessing = false;
        return;
      }

      // Attempt verification with nHost
      const result = await nhost.auth.verifyEmail({
        refreshToken: token
      });

      console.log('Verification result:', result);

      if (result.error) {
        console.error('Verification failed:', result.error);
        
        // Handle different error types
        if (result.error.message?.includes('expired') || result.error.message?.includes('invalid')) {
          // Check if account exists and is already verified
          await checkAccountStatus(token);
        } else {
          verificationStatus = 'failed';
          errorMessage = result.error.message || 'Verification failed';
        }
      } else {
        // Verification successful - but don't stay logged in
        verificationStatus = 'success';
        userEmail = result.session?.user?.email || '';
        
        // Sign out immediately to prevent auto-dashboard redirect
        await nhost.auth.signOut();
        
        // Redirect to welcome after short delay to show success message
        setTimeout(() => {
          goto('/welcome');
        }, 2000);
      }

    } catch (error) {
      console.error('Verification error:', error);
      verificationStatus = 'failed';
      errorMessage = 'An unexpected error occurred';
    } finally {
      isProcessing = false;
    }
  }

  async function checkAccountStatus(token: string) {
    try {
      // Try to extract email from token or check account status
      // For now, we'll assume the account exists but token is expired
      verificationStatus = 'failed';
      errorMessage = 'Verification link has expired';
    } catch (error) {
      console.error('Account status check failed:', error);
      verificationStatus = 'failed';
      errorMessage = 'Verification link is invalid';
    }
  }

  function redirectToLogin(reason: string) {
    const params = new URLSearchParams();
    params.set('message', reason);
    if (userEmail) {
      params.set('email', userEmail);
    }
    goto(`/?${params.toString()}`);
  }

  function handleResendVerification() {
    goto('/resendverification');
  }
</script>

<div class="auth-container">
  <Card class="auth-card">
    <CardContent class="auth-content">
      {#if isProcessing}
        <!-- Processing State -->
        <div class="status-state">
          <div class="status-icon processing">
            <Clock size={48} />
          </div>
          <div class="status-content">
            <h3 class="status-title">Verifying your email...</h3>
            <p class="status-message">Please wait while we verify your account.</p>
          </div>
        </div>
        
      {:else if verificationStatus === 'success'}
        <!-- Success State -->
        <div class="status-state">
          <div class="status-icon success">
            <CheckCircle size={48} />
          </div>
          <div class="status-content">
            <h3 class="status-title success">Email Verified Successfully!</h3>
            <p class="status-message">
              Your email has been verified. You will be redirected to welcome page shortly.
            </p>
            <div class="status-actions">
              <Button onclick={() => goto('/welcome')} class="w-full">
                Continue to Welcome
              </Button>
            </div>
          </div>
        </div>
        
      {:else if verificationStatus === 'already_verified'}
        <!-- Already Verified State -->
        <div class="status-state">
          <div class="status-icon info">
            <CheckCircle size={48} />
          </div>
          <div class="status-content">
            <h3 class="status-title info">Already Verified</h3>
            <p class="status-message">
              Your email is already verified. Please log in to access your account.
            </p>
            <div class="status-actions">
              <Button onclick={() => redirectToLogin('already_verified')} class="w-full">
                Go to Login
              </Button>
            </div>
          </div>
        </div>
        
      {:else if verificationStatus === 'failed'}
        <!-- Failed State -->
        <div class="status-state">
          <div class="status-icon error">
            <XCircle size={48} />
          </div>
          <div class="status-content">
            <h3 class="status-title error">Verification Failed</h3>
            <p class="status-message">{errorMessage}</p>
            <div class="status-actions">
              <Button onclick={handleResendVerification} class="w-full">
                Get New Verification Link
              </Button>
              <Button variant="outline" onclick={() => redirectToLogin('failed')} class="w-full">
                Go to Login
              </Button>
            </div>
          </div>
        </div>
        
      {:else if verificationStatus === 'invalid'}
        <!-- Invalid Link State -->
        <div class="status-state">
          <div class="status-icon warning">
            <AlertCircle size={48} />
          </div>
          <div class="status-content">
            <h3 class="status-title warning">Invalid Link</h3>
            <p class="status-message">
              This verification link is invalid or malformed.
            </p>
            <div class="status-actions">
              <Button onclick={() => goto('/')} class="w-full">
                Create New Account
              </Button>
              <Button variant="outline" onclick={handleResendVerification} class="w-full">
                Resend Verification
              </Button>
            </div>
          </div>
        </div>
      {/if}

      <!-- Back to Login -->
      <div class="back-to-login">
        <a href="/" class="back-link">
          <ArrowLeft size={16} />
          Back to login
        </a>
      </div>
    </CardContent>
  </Card>
</div>

<style>
  .auth-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
  }

  :global(.auth-card) {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  :global(.dark .auth-card) {
    background: rgba(2, 6, 23, 0.95);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(148, 163, 184, 0.1);
  }

  :global(.auth-content) {
    padding-top: 1.5rem;
  }

  .status-state {
    text-align: center;
    padding: 1rem 0;
    animation: statusFadeIn 0.6s ease-out;
  }

  @keyframes statusFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .status-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .status-icon.processing {
    color: hsl(var(--muted-foreground));
    animation: pulse 2s ease-in-out infinite;
  }

  .status-icon.success {
    color: rgb(34, 197, 94);
  }

  .status-icon.error {
    color: rgb(239, 68, 68);
  }

  .status-icon.warning {
    color: rgb(245, 158, 11);
  }

  .status-icon.info {
    color: rgb(59, 130, 246);
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .status-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .status-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .status-title.success {
    color: rgb(34, 197, 94);
  }

  .status-title.error {
    color: rgb(239, 68, 68);
  }

  .status-title.warning {
    color: rgb(245, 158, 11);
  }

  .status-title.info {
    color: rgb(59, 130, 246);
  }

  .status-message {
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  .status-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .back-to-login {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
  }

  .back-link:hover {
    background: hsl(var(--muted));
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-container {
      max-width: 100%;
      padding: 0 1rem;
    }

    :global(.auth-card) {
      border-radius: 16px;
    }

    :global(.auth-content) {
      padding: 1rem;
    }

    .status-message {
      font-size: 0.875rem;
    }
  }
</style>
